import config, {
  acl_id_1,
  gateway_id,
  connCtrlType,
  getGatewayServiceInfoList,
  getBusinessServiceInfoList,
} from "./ext-iscp-config";

const userAgent = window.navigator.userAgent
// console.log('userAgent: ', userAgent);

// ext_ISCP_RhConfig
// ext_ISCP_RhConnectCtrl
// ext_ISCP_RhConnState
// ext_ISCP_RhLocalPortCtrl
const $wx = wx;

export function ext_ISCP_RhConfig() {
  const businessServiceInfoList = getBusinessServiceInfoList();
  const gatewayServiceInfoList = getGatewayServiceInfoList();

  return new Promise((resolve, reject) => {
    const data = {
      businessServiceInfoList,
      gatewayServiceInfoList,
    };
    console.log("ext_ISCP_RhConfig 配置", data);

    $wx.invoke("ext_ISCP_RhConfig", { data }, function(res) {
      console.log("ext_ISCP_RhConfig 结束", res);

      const { err_msg, result } = res;
      if (err_msg === "ext_ISCP_RhConfig:ok") {
        resolve(result);
      }
      if (err_msg === "ext_ISCP_RhConfig:fail") {
        reject(result);
      }
      if (err_msg === "ext_ISCP_RhConfig:cancel") {
        reject(result);
      }
      if (err_msg === "ext_ISCP_RhConfig:not_support") {
        reject(result);
      }
    });
    
  });
}

export function ext_ISCP_RhConnectCtrl() {
  // return new Promise((resolve, reject) => {
  //   setTimeout(() => {
  //     flag ? resolve() : reject();
  //   }, 700);
  // });

  return new Promise((resolve, reject) => {
    const data = {
      gatewayServiceId: gateway_id,
      connCtrlType: connCtrlType.connect,
    };
    console.log("ext_ISCP_RhConnectCtrl 配置", data);

    $wx.invoke("ext_ISCP_RhConnectCtrl", { data }, function(res) {
      console.log("ext_ISCP_RhConnectCtrl 结束", res);

      const { err_msg, result } = res;
      if (err_msg === "ext_ISCP_RhConnectCtrl:ok") {
        resolve(result);
      }
      if (err_msg === "ext_ISCP_RhConnectCtrl:fail") {
        reject(result);
      }
      if (err_msg === "ext_ISCP_RhConnectCtrl:cancel") {
        reject(result);
      }
      if (err_msg === "ext_ISCP_RhConnectCtrl:not_support") {
        reject(result);
      }
    });
  });
}

/**
 * 2022-5-20	err_msg	String	判断成功依据：ok成功 fail 失败 cancel 取消 not_support 不支持
 * 2022-5-20	result	String	Android（0-连接失败，2连接成功） IOS(0-连接失败，1-连接成功)
 * 2022-5-20	linkState	String	0-连接失败，2连接成功
 */
export function ext_ISCP_RhConnState() {
  // return new Promise((resolve, reject) => {
  //   setTimeout(() => {
  //     if (flag) {
  //       resolve({ result: "", linkState: "2" });
  //     } else {
  //       reject({ result: "err", linkState: "0" });
  //     }
  //   }, 2000);
  // });

  return new Promise((resolve, reject) => {
    const data = {
      gatewayServiceId: gateway_id,
    };
    console.log("ext_ISCP_RhConnState 配置", data);

    $wx.invoke("ext_ISCP_RhConnState", { data }, function(res) {
      console.log("ext_ISCP_RhConnState 结束", res);

      // TODO 判断安卓IOS
      const { err_msg, result, linkState } = res;
      if (err_msg === "ext_ISCP_RhConnState:ok") {

        if (result !== "0" && linkState === "2") {
          resolve({ result, linkState });
        } else {
          reject({ result, linkState });
        }
        
      }
      if (err_msg === "ext_ISCP_RhConnState:fail") {
        reject({ result, linkState });
      }
      if (err_msg === "ext_ISCP_RhConnState:cancel") {
        reject({ result, linkState });
      }
      if (err_msg === "ext_ISCP_RhConnState:not_support") {
        reject({ result, linkState });
      }
    });
  });
}

export function ext_ISCP_RhLocalPortCtrl() {
  // return new Promise((resolve, reject) => {
  //   resolve(10086);
  // });

  return new Promise((resolve, reject) => {
    const data = {
      gatewayServiceId: gateway_id,
      aclId: acl_id_1,
    };
    console.log("ext_ISCP_RhLocalPortCtrl 配置", data);

    $wx.invoke("ext_ISCP_RhLocalPortCtrl", { data }, function(res) {
      console.log("ext_ISCP_RhLocalPortCtrl 结束", res);

      const { err_msg, result, port } = res;
      if (err_msg === "ext_ISCP_RhLocalPortCtrl:ok") {
        resolve(port);
      }
      if (err_msg === "ext_ISCP_RhLocalPortCtrl:fail") {
        reject(result);
      }
      if (err_msg === "ext_ISCP_RhLocalPortCtrl:cancel") {
        reject(result);
      }
      if (err_msg === "ext_ISCP_RhLocalPortCtrl:not_support") {
        reject(result);
      }
    });
  });
}

async function getPort(retry = 0, fail = 0, isRetry = false) {
  if (fail === 3) {
    throw new Error("安全交互平台连接中断");
  }

  if (isRetry) {
    try {
      await ext_ISCP_RhConnectCtrl();

      retry += 1;
      console.log(`第${retry}次 ext_ISCP_RhConnectCtrl重连 成功`);

      return await getPort(0, fail, false);
    } catch (error) {
      retry += 1;
      fail += 1;
      console.log(
        `第${retry}次 ext_ISCP_RhConnectCtrl重连 失败, 进行第${retry + 1}次连接`
      );

      return await getPort(retry, fail, true);
    }
  }

  try {
    const { result, linkState } = await ext_ISCP_RhConnState();
    if (linkState === "2") {
      const port = await ext_ISCP_RhLocalPortCtrl();
      return port;
    }
  } catch (error) {
    console.log("无法获取端口， 进行重连");
    return await getPort(retry, fail, true);
  }
}

export function getPortConfig() {
  let retry = 0;
  let fail = 0;
  return {
    retry,
    fail,
    getISCPPort: getPort,
  };
}
