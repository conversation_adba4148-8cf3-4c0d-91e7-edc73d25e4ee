{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20200728.110236.13756.0.001.json", "dumpEventTime": "2020-07-28T11:02:36Z", "dumpEventTimeStamp": "1595905356833", "processId": 13756, "cwd": "C:\\Users\\<USER>\\Desktop\\wxapp2", "commandLine": ["node", "C:\\Users\\<USER>\\Desktop\\wxapp2\\node_modules\\.bin\\\\..\\_@vue_cli-service@4.3.1@@vue\\cli-service\\bin\\vue-cli-service.js", "serve"], "nodejsVersion": "v12.13.1", "wordSize": 64, "arch": "x64", "platform": "win32", "componentVersions": {"node": "12.13.1", "v8": "7.7.299.13-node.16", "uv": "1.33.1", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.39.2", "napi": "5", "llhttp": "1.1.4", "http_parser": "2.8.0", "openssl": "1.1.1d", "cldr": "35.1", "icu": "64.2", "tz": "2019c", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.13.1/node-v12.13.1-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.13.1/node-v12.13.1.tar.gz", "libUrl": "https://nodejs.org/download/release/v12.13.1/win-x64/node.lib"}, "osName": "Windows_NT", "osRelease": "10.0.18363", "osVersion": "Windows 10 Enterprise", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i5-5300U CPU @ 2.30GHz", "speed": 2295, "user": 19584609, "nice": 0, "sys": 5742500, "idle": 30088859, "irq": 198500}, {"model": "Intel(R) Core(TM) i5-5300U CPU @ 2.30GHz", "speed": 2295, "user": 21154953, "nice": 0, "sys": 5010765, "idle": 29249203, "irq": 45250}, {"model": "Intel(R) Core(TM) i5-5300U CPU @ 2.30GHz", "speed": 2295, "user": 25681062, "nice": 0, "sys": 5596796, "idle": 24137109, "irq": 26015}, {"model": "Intel(R) Core(TM) i5-5300U CPU @ 2.30GHz", "speed": 2295, "user": 27592281, "nice": 0, "sys": 5512718, "idle": 22309968, "irq": 16109}], "networkInterfaces": [{"name": "WLAN", "internal": false, "mac": "e4:02:9b:53:f0:2a", "address": "2408:84f2:408:7900:f9b7:4eca:2da3:1b3", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 0}, {"name": "WLAN", "internal": false, "mac": "e4:02:9b:53:f0:2a", "address": "2408:84f2:408:7900:f469:dd37:dec3:3c05", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "WLAN", "internal": false, "mac": "e4:02:9b:53:f0:2a", "address": "fe80::f9b7:4eca:2da3:1b3", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 10}, {"name": "WLAN", "internal": false, "mac": "e4:02:9b:53:f0:2a", "address": "**************", "netmask": "*************", "family": "IPv4"}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}], "host": "wurongchang"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00007ff722771729", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+10873"}, {"pc": "0x00007ff722775b4c", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+28316"}, {"pc": "0x00007ff722774b08", "symbol": "std::basic_ostream<char,std::char_traits<char> >::operator<<+24152"}, {"pc": "0x00007ff72286369b", "symbol": "v8::base::CPU::has_sse+37723"}, {"pc": "0x00007ff7230682de", "symbol": "v8::Isolate::ReportExternalAllocationLimitReached+94"}, {"pc": "0x00007ff723050321", "symbol": "v8::SharedArrayBuffer::Externalize+833"}, {"pc": "0x00007ff722f1dbec", "symbol": "v8::internal::Heap::EphemeronKeyWriteBarrierFromCode+1436"}, {"pc": "0x00007ff722f28f90", "symbol": "v8::internal::Heap::ProtectUnprotectedMemoryChunks+1312"}, {"pc": "0x00007ff722f25ac4", "symbol": "v8::internal::Heap::PageFlagsAreConsistent+3204"}, {"pc": "0x00007ff722f1b353", "symbol": "v8::internal::Heap::CollectGarbage+1283"}, {"pc": "0x00007ff722f19b24", "symbol": "v8::internal::Heap::AddRetainedMap+2356"}, {"pc": "0x00007ff722f3adf5", "symbol": "v8::internal::Factory::NewFillerObject+53"}, {"pc": "0x00007ff722ca6e19", "symbol": "v8::internal::interpreter::JumpTableTargetOffsets::iterator::operator=+4057"}, {"pc": "0x00007ff72349404d", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+567949"}, {"pc": "0x00007ff723413393", "symbol": "v8::internal::SetupIsolateDelegate::SetupHeap+40403"}, {"pc": "0x0000033387fb064b", "symbol": ""}], "javascriptHeap": {"totalMemory": 2151362560, "totalCommittedMemory": 2151362560, "usedMemory": 2136891928, "availableMemory": 48450152, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 262144, "capacity": 261872, "used": 32296, "available": 229576}, "new_space": {"memorySize": 2097152, "committedMemory": 2097152, "capacity": 1047488, "used": 385472, "available": 662016}, "old_space": {"memorySize": 2065805312, "committedMemory": 2065805312, "capacity": 2055678640, "used": 2055620304, "available": 58336}, "code_space": {"memorySize": 2260992, "committedMemory": 2260992, "capacity": 1860416, "used": 1860416, "available": 0}, "map_space": {"memorySize": 4198400, "committedMemory": 4198400, "capacity": 2540480, "used": 2540480, "available": 0}, "large_object_space": {"memorySize": 75534336, "committedMemory": 75534336, "capacity": 75352544, "used": 75352544, "available": 0}, "code_large_object_space": {"memorySize": 1204224, "committedMemory": 1204224, "capacity": 1100416, "used": 1100416, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 1047488, "used": 0, "available": 1047488}}}, "resourceUsage": {"userCpuSeconds": 351.265, "kernelCpuSeconds": 190.468, "cpuConsumptionPercent": 17.7095, "maxRss": 2256605184, "pageFaults": {"IORequired": 1393590, "IONotRequired": 0}, "fsActivity": {"reads": 5786, "writes": 70}}, "libuv": [], "environmentVariables": {"=C:": "C:\\Users\\<USER>\\Desktop\\wxapp2", "ALLUSERSPROFILE": "C:\\ProgramData", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "BABEL_ENV": "development", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "WURONGCHANG", "ComSpec": "C:\\Windows\\system32\\cmd.exe", "dp0": "C:\\Users\\<USER>\\Desktop\\wxapp2\\node_modules\\.bin\\", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "FLUTTER_STORAGE_BASE_URL": "https://storage.flutter-io.cn", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\Administrator", "INIT_CWD": "C:\\Users\\<USER>\\Desktop\\wxapp2", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\WURONGCHANG", "NODE": "D:\\webSoft\\nodejs\\node.exe", "NODE_ENV": "development", "NODE_EXE": "D:\\webSoft\\nodejs\\\\node.exe", "NPM_CLI_JS": "D:\\webSoft\\nodejs\\\\node_modules\\npm\\bin\\npm-cli.js", "npm_config_access": "", "npm_config_allow_same_version": "", "npm_config_also": "", "npm_config_always_auth": "", "npm_config_argv": "{\"remain\":[],\"cooked\":[\"run\",\"serve\"],\"original\":[\"run\",\"serve\"]}", "npm_config_audit": "true", "npm_config_audit_level": "low", "npm_config_auth_type": "legacy", "npm_config_before": "", "npm_config_bin_links": "true", "npm_config_browser": "", "npm_config_ca": "", "npm_config_cache": "C:\\Users\\<USER>\\AppData\\Roaming\\npm-cache", "npm_config_cache_lock_retries": "10", "npm_config_cache_lock_stale": "60000", "npm_config_cache_lock_wait": "10000", "npm_config_cache_max": "Infinity", "npm_config_cache_min": "10", "npm_config_cafile": "", "npm_config_cert": "", "npm_config_cidr": "", "npm_config_color": "true", "npm_config_commit_hooks": "true", "npm_config_depth": "Infinity", "npm_config_description": "true", "npm_config_dev": "", "npm_config_dry_run": "", "npm_config_editor": "notepad.exe", "npm_config_engine_strict": "", "npm_config_fetch_retries": "2", "npm_config_fetch_retry_factor": "10", "npm_config_fetch_retry_maxtimeout": "60000", "npm_config_fetch_retry_mintimeout": "10000", "npm_config_force": "", "npm_config_format_package_lock": "true", "npm_config_git": "git", "npm_config_git_tag_version": "true", "npm_config_global": "", "npm_config_globalconfig": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmrc", "npm_config_globalignorefile": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\etc\\npmignore", "npm_config_global_style": "", "npm_config_group": "", "npm_config_ham_it_up": "", "npm_config_heading": "npm", "npm_config_https_proxy": "", "npm_config_if_present": "", "npm_config_ignore_prepublish": "", "npm_config_ignore_scripts": "", "npm_config_init_author_email": "", "npm_config_init_author_name": "", "npm_config_init_author_url": "", "npm_config_init_license": "ISC", "npm_config_init_module": "C:\\Users\\<USER>\\.npm-init.js", "npm_config_init_version": "1.0.0", "npm_config_json": "", "npm_config_key": "", "npm_config_legacy_bundling": "", "npm_config_link": "", "npm_config_local_address": "", "npm_config_loglevel": "notice", "npm_config_logs_max": "10", "npm_config_long": "", "npm_config_maxsockets": "50", "npm_config_message": "%s", "npm_config_metrics_registry": "https://registry.npmjs.org/", "npm_config_node_gyp": "D:\\webSoft\\nodejs\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_node_options": "", "npm_config_node_version": "12.13.1", "npm_config_noproxy": "", "npm_config_offline": "", "npm_config_onload_script": "", "npm_config_only": "", "npm_config_optional": "true", "npm_config_otp": "", "npm_config_package_lock": "true", "npm_config_package_lock_only": "", "npm_config_parseable": "", "npm_config_prefer_offline": "", "npm_config_prefer_online": "", "npm_config_prefix": "C:\\Users\\<USER>\\AppData\\Roaming\\npm", "npm_config_preid": "", "npm_config_production": "", "npm_config_progress": "true", "npm_config_proxy": "", "npm_config_read_only": "", "npm_config_rebuild_bundle": "true", "npm_config_registry": "https://registry.npmjs.org/", "npm_config_rollback": "true", "npm_config_save": "true", "npm_config_save_bundle": "", "npm_config_save_dev": "", "npm_config_save_exact": "", "npm_config_save_optional": "", "npm_config_save_prefix": "^", "npm_config_save_prod": "", "npm_config_scope": "", "npm_config_scripts_prepend_node_path": "warn-only", "npm_config_script_shell": "", "npm_config_searchexclude": "", "npm_config_searchlimit": "20", "npm_config_searchopts": "", "npm_config_searchstaleness": "900", "npm_config_send_metrics": "", "npm_config_shell": "C:\\Windows\\system32\\cmd.exe", "npm_config_shrinkwrap": "true", "npm_config_sign_git_commit": "", "npm_config_sign_git_tag": "", "npm_config_sso_poll_frequency": "500", "npm_config_sso_type": "o<PERSON>h", "npm_config_strict_ssl": "true", "npm_config_tag": "latest", "npm_config_tag_version_prefix": "v", "npm_config_timing": "", "npm_config_tmp": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "npm_config_umask": "0000", "npm_config_unicode": "", "npm_config_unsafe_perm": "true", "npm_config_update_notifier": "true", "npm_config_usage": "", "npm_config_user": "", "npm_config_userconfig": "C:\\Users\\<USER>\\.npmrc", "npm_config_user_agent": "npm/6.12.1 node/v12.13.1 win32 x64", "npm_config_version": "", "npm_config_versions": "", "npm_config_viewer": "browser", "npm_execpath": "D:\\webSoft\\nodejs\\node_modules\\npm\\bin\\npm-cli.js", "npm_lifecycle_event": "serve", "npm_lifecycle_script": "vue-cli-service serve", "npm_node_execpath": "D:\\webSoft\\nodejs\\node.exe", "npm_package_browserslist_0": "> 1%", "npm_package_browserslist_1": "last 2 versions", "npm_package_browserslist_2": "not dead", "npm_package_dependencies_axios": "^0.19.2", "npm_package_dependencies_core_js": "^3.6.5", "npm_package_dependencies_echarts": "^4.8.0", "npm_package_dependencies_js_sha1": "^0.6.0", "npm_package_dependencies_qs": "^6.9.4", "npm_package_dependencies_sign_canvas": "^1.0.6", "npm_package_dependencies_vconsole": "^3.3.4", "npm_package_dependencies_view_design": "^4.2.0", "npm_package_dependencies_vue": "^2.6.11", "npm_package_dependencies_vue_baidu_map": "^0.21.22", "npm_package_dependencies_vue_pdf": "^4.1.0", "npm_package_dependencies_vue_router": "^3.3.2", "npm_package_dependencies_v_charts": "^1.19.0", "npm_package_dependencies_webpack_require_http": "^0.4.3", "npm_package_dependencies__nutui_nutui": "^2.2.5", "npm_package_description": "```\r npm install\r ```", "npm_package_devDependencies_babel_eslint": "^10.1.0", "npm_package_devDependencies_eslint": "^6.7.2", "npm_package_devDependencies_eslint_plugin_vue": "^6.2.2", "npm_package_devDependencies_vue_template_compiler": "^2.6.11", "npm_package_devDependencies__vue_cli_plugin_babel": "~4.3.0", "npm_package_devDependencies__vue_cli_plugin_eslint": "~4.3.0", "npm_package_devDependencies__vue_cli_service": "~4.3.0", "npm_package_eslintConfig_env_node": "false", "npm_package_eslintConfig_root": "false", "npm_package_gitHead": "a3389113fa1d140a0af18de5773209c6474947cf", "npm_package_name": "wxapp2", "npm_package_private": "true", "npm_package_readmeFilename": "README.md", "npm_package_scripts_build": "vue-cli-service build", "npm_package_scripts_lint": "vue-cli-service lint", "npm_package_scripts_serve": "vue-cli-service serve", "npm_package_version": "0.1.0", "NPM_PREFIX_NPM_CLI_JS": "C:\\Users\\<USER>\\AppData\\Roaming\\npm\\node_modules\\npm\\bin\\npm-cli.js", "NUMBER_OF_PROCESSORS": "4", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OS": "Windows_NT", "Path": "D:\\webSoft\\nodejs\\node_modules\\npm\\node_modules\\npm-lifecycle\\node-gyp-bin;C:\\Users\\<USER>\\Desktop\\wxapp2\\node_modules\\.bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\Intel\\Intel(R) Management Engine Components\\DAL;C:\\Program Files\\Intel\\Intel(R) Management Engine Components\\DAL;D:\\webSoft\\nodejs;C:\\Program Files\\Intel\\WiFi\\bin\\;C:\\Program Files\\Common Files\\Intel\\WirelessCommon\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\Java\\jdk1.8.0_211\\bin;C:\\Program Files\\Java\\jdk1.8.0_211\\jre\\bin;D:\\flutter\\bin;D:\\flutter\\bin\\cache\\dart-sdk\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python38\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Roaming\\npm;D:\\webSoft\\Microsoft VS Code\\bin;D:\\webSoft\\MongoDB\\bin;C:\\Program Files\\Java\\jdk1.8.0_211;", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 61 Stepping 4, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "3d04", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "PUB_HOSTED_URL": "https://pub.flutter-io.cn", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "SystemDrive": "C:", "SystemRoot": "C:\\Windows", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "USERDOMAIN": "WURONGCHANG", "USERDOMAIN_ROAMINGPROFILE": "WURONGCHANG", "USERNAME": "Administrator", "USERPROFILE": "C:\\Users\\<USER>", "VUE_CLI_ENTRY_FILES": "[\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\wxapp2\\\\src\\\\main.js\"]", "VUE_CLI_TRANSPILE_BABEL_RUNTIME": "true", "WEBPACK_DEV_SERVER": "true", "windir": "C:\\Windows", "_prog": "node"}, "sharedObjects": ["D:\\webSoft\\nodejs\\node.exe", "C:\\Windows\\SYSTEM32\\ntdll.dll", "C:\\Windows\\System32\\KERNEL32.DLL", "C:\\Windows\\System32\\KERNELBASE.dll", "C:\\Windows\\System32\\WS2_32.dll", "C:\\Windows\\System32\\RPCRT4.dll", "C:\\Windows\\SYSTEM32\\dbghelp.dll", "C:\\Windows\\System32\\ADVAPI32.dll", "C:\\Windows\\System32\\ucrtbase.dll", "C:\\Windows\\System32\\msvcrt.dll", "C:\\Windows\\System32\\sechost.dll", "C:\\Windows\\System32\\USER32.dll", "C:\\Windows\\System32\\win32u.dll", "C:\\Windows\\System32\\GDI32.dll", "C:\\Windows\\System32\\gdi32full.dll", "C:\\Windows\\System32\\msvcp_win.dll", "C:\\Windows\\System32\\PSAPI.DLL", "C:\\Windows\\System32\\CRYPT32.dll", "C:\\Windows\\System32\\MSASN1.dll", "C:\\Windows\\SYSTEM32\\IPHLPAPI.DLL", "C:\\Windows\\System32\\bcrypt.dll", "C:\\Windows\\SYSTEM32\\USERENV.dll", "C:\\Windows\\System32\\profapi.dll", "C:\\Windows\\SYSTEM32\\WINMM.dll", "C:\\Windows\\SYSTEM32\\WINMMBASE.dll", "C:\\Windows\\System32\\cfgmgr32.dll", "C:\\Windows\\System32\\bcryptPrimitives.dll", "C:\\Windows\\System32\\IMM32.DLL", "C:\\Windows\\System32\\powrprof.dll", "C:\\Windows\\System32\\UMPDC.dll", "C:\\Windows\\SYSTEM32\\CRYPTBASE.DLL", "C:\\Windows\\system32\\uxtheme.dll", "C:\\Windows\\System32\\combase.dll", "D:\\Program Files (x86)\\Tencent\\QMProxyAccelerator\\3.0.6406.138\\QMProxyAccLsp64.dll", "C:\\Windows\\System32\\SHLWAPI.dll", "C:\\Windows\\system32\\mswsock.dll", "C:\\Windows\\system32\\wshqos.dll", "C:\\Windows\\SYSTEM32\\wshtcpip.DLL", "C:\\Windows\\SYSTEM32\\wship6.dll", "C:\\Windows\\System32\\kernel.appcore.dll", "C:\\Windows\\system32\\napinsp.dll", "C:\\Windows\\system32\\pnrpnsp.dll", "C:\\Windows\\SYSTEM32\\DNSAPI.dll", "C:\\Windows\\System32\\NSI.dll", "C:\\Windows\\System32\\winrnr.dll", "C:\\Windows\\system32\\NLAapi.dll", "C:\\Windows\\system32\\wshbth.dll", "C:\\Windows\\SYSTEM32\\dhcpcsvc6.DLL", "C:\\Windows\\SYSTEM32\\dhcpcsvc.DLL"]}