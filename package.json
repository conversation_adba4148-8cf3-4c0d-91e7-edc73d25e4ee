{"name": "wxapp2", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "postbuild": "node move.js"}, "dependencies": {"@nutui/nutui": "^2.2.10", "axios": "^0.19.2", "crypto-js": "^4.2.0", "default-passive-events": "^2.0.0", "docx-preview": "^0.1.18", "echarts": "^4.9.0", "element-ui": "^2.14.1", "html5-qrcode": "^2.3.8", "image-conversion": "^2.1.1", "jquery": "^3.6.0", "js-sha1": "^0.6.0", "jsencrypt": "^3.3.2", "mint-ui": "^2.2.13", "pdfjs-dist": "^2.5.207", "qs": "^6.9.4", "sign-canvas": "^1.0.6", "v-charts": "^1.19.0", "vant": "^2.12.48", "vconsole": "^3.3.4", "view-design": "^4.2.0", "vue": "^2.6.11", "vue-baidu-map": "^0.21.22", "vue-pdf": "^4.1.0", "vue-pdf-embed": "^1.1.6", "vue-router": "^3.3.2", "vue3-pdfjs": "^0.1.6", "vuex": "^3.5.1", "webpack-require-http": "^0.4.3"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "~4.3.0", "@vue/cli-service": "~4.3.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "fs-extra": "^11.2.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": false, "env": {"node": false}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}